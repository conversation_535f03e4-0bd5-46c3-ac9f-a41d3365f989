import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Phone, ArrowRight, RotateCcw, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { useAuth } from '../../context/AuthContext';
import { validatePhone, validateOTP } from '../../services/authAPI';

const OTPLogin = ({ onSuccess, onClose }) => {
  const [step, setStep] = useState('phone'); // 'phone' | 'otp' | 'profile'
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [userDetails, setUserDetails] = useState({
    first_name: '',
    last_name: '',
    email: ''
  });
  const [resendTimer, setResendTimer] = useState(0);
  const [errors, setErrors] = useState({});

  const { sendOTP, verifyOTP, resendOTP, isLoading, error, otpSent, originalPhone, clearError } = useAuth();

  // Auto-advance to OTP step when OTP is sent
  useEffect(() => {
    if (otpSent && step === 'phone') {
      console.log('Advancing to OTP step, current phone state:', phone);
      setStep('otp');
      setResendTimer(60);
    }
  }, [otpSent, step, phone]);

  const otpRefs = useRef([]);
  const phoneInputRef = useRef(null);

  // Timer for resend OTP
  useEffect(() => {
    let interval;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  // Focus phone input on mount
  useEffect(() => {
    if (phoneInputRef.current) {
      phoneInputRef.current.focus();
    }
  }, []);

  // Clear errors when inputs change
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => clearError(), 5000);
      return () => clearTimeout(timer);
    }
  }, [error, clearError]);

  // Format phone number display
  const formatPhoneDisplay = (value) => {
    const cleaned = value.replace(/\D/g, '');
    if (cleaned.length <= 10) {
      return cleaned.replace(/(\d{5})(\d{5})/, '$1 $2');
    }
    return cleaned;
  };

  // Handle phone input
  const handlePhoneChange = (e) => {
    const value = e.target.value.replace(/\D/g, '');
    if (value.length <= 10) {
      setPhone(value);
      setErrors(prev => ({ ...prev, phone: '' }));
    }
  };

  // Handle OTP input
  const handleOTPChange = (index, value) => {
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }

    setErrors(prev => ({ ...prev, otp: '' }));
  };

  // Handle OTP backspace
  const handleOTPKeyDown = (index, e) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  // Handle send OTP
  const handleSendOTP = async () => {
    setErrors({});

    // Prevent duplicate calls
    if (isLoading) {
      console.log('Already sending OTP, ignoring duplicate call');
      return;
    }

    // Validate phone
    if (!validatePhone(phone)) {
      setErrors({ phone: 'Please enter a valid 10-digit phone number' });
      return;
    }

    console.log('Calling sendOTP with phone:', phone);
    const result = await sendOTP(phone);
    console.log('SendOTP result:', result);

    // The useEffect will handle step transition based on otpSent
    if (!result.success) {
      setErrors({ phone: result.error });
    } else {
      // Clear any previous errors on success
      clearError();
    }
  };

  // Handle verify OTP
  const handleVerifyOTP = async () => {
    setErrors({});

    const otpString = otp.join('');

    // Use originalPhone from AuthContext instead of local phone state
    const phoneToUse = originalPhone || phone;

    // Debug logging
    console.log('handleVerifyOTP called with local phone:', phone, 'originalPhone:', originalPhone, 'using:', phoneToUse, 'otp:', otpString.substring(0, 2) + '****');

    // Validate OTP
    if (!validateOTP(otpString)) {
      setErrors({ otp: 'Please enter a valid 6-digit OTP' });
      return;
    }

    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      if (result.data.is_new_user && (!userDetails.first_name || !userDetails.last_name)) {
        setStep('profile');
      } else {
        onSuccess?.(result.data);
      }
    }
  };

  // Handle profile completion
  const handleCompleteProfile = async () => {
    setErrors({});

    // Validate required fields
    const newErrors = {};
    if (!userDetails.first_name.trim()) {
      newErrors.first_name = 'First name is required';
    }
    if (!userDetails.last_name.trim()) {
      newErrors.last_name = 'Last name is required';
    }
    if (userDetails.email && !/\S+@\S+\.\S+/.test(userDetails.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    const otpString = otp.join('');
    const phoneToUse = originalPhone || phone;
    const result = await verifyOTP(phoneToUse, otpString, userDetails);

    if (result.success) {
      onSuccess?.(result.data);
    }
  };

  // Handle resend OTP
  const handleResendOTP = async () => {
    const phoneToUse = originalPhone || phone;
    const result = await resendOTP(phoneToUse);
    if (result.success) {
      setResendTimer(60);
      setOtp(['', '', '', '', '', '']);
      otpRefs.current[0]?.focus();
    }
  };



  return (
    <div className="w-full max-w-md mx-auto" data-step={step}>
      <AnimatePresence mode="wait">
        {/* Phone Number Step */}
        {step === 'phone' && (
          <motion.div
            key="phone"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-2">Welcome to Wolffoxx</h2>
              <p className="text-[#AAAAAA]">Enter your phone number to continue</p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                  Phone Number
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Phone size={18} className="text-[#6a6a6a]" />
                    <span className="ml-2 text-[#AAAAAA]">+91</span>
                  </div>
                  <input
                    ref={phoneInputRef}
                    type="tel"
                    value={formatPhoneDisplay(phone)}
                    onChange={handlePhoneChange}
                    placeholder="98765 43210"
                    className="w-full pl-16 pr-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white placeholder-[#6a6a6a] focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }} // Mobile touch target
                  />
                </div>
                {errors.phone && (
                  <p className="mt-1 text-sm text-red-400 flex items-center gap-1">
                    <AlertCircle size={14} />
                    {errors.phone}
                  </p>
                )}
              </div>

              <motion.button
                onClick={handleSendOTP}
                disabled={isLoading || !phone}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-[#404040] disabled:to-[#404040] text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                style={{ minHeight: '44px' }}
                whileHover={{ scale: phone && !isLoading ? 1.02 : 1 }}
                whileTap={{ scale: phone && !isLoading ? 0.98 : 1 }}
              >
                {isLoading ? (
                  <Loader2 size={18} className="animate-spin" />
                ) : (
                  <>
                    Send OTP
                    <ArrowRight size={18} />
                  </>
                )}
              </motion.button>
            </div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-center gap-2"
              >
                <AlertCircle size={16} />
                {error}
              </motion.div>
            )}
          </motion.div>
        )}

        {/* OTP Verification Step */}
        {step === 'otp' && (
          <motion.div
            key="otp"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-2xl font-bold text-white mb-2">Verify OTP</h2>
              <p className="text-[#AAAAAA]">
                Enter the 6-digit code sent to +91 {formatPhoneDisplay(phone)}
              </p>
              {/* Debug info */}
              <p className="text-xs text-gray-500 mt-1">
                Debug: phone state = "{phone}" (length: {phone.length})
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-[#AAAAAA] mb-3">
                  Enter OTP
                </label>
                <div className="flex gap-2 justify-center">
                  {otp.map((digit, index) => (
                    <input
                      key={index}
                      ref={el => otpRefs.current[index] = el}
                      type="text"
                      inputMode="numeric"
                      maxLength={1}
                      value={digit}
                      onChange={(e) => handleOTPChange(index, e.target.value)}
                      onKeyDown={(e) => handleOTPKeyDown(index, e)}
                      className="w-12 h-12 text-center text-lg font-bold bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    />
                  ))}
                </div>
                {errors.otp && (
                  <p className="mt-2 text-sm text-red-400 flex items-center justify-center gap-1">
                    <AlertCircle size={14} />
                    {errors.otp}
                  </p>
                )}
              </div>

              <motion.button
                onClick={handleVerifyOTP}
                disabled={isLoading || otp.some(digit => !digit)}
                className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-[#404040] disabled:to-[#404040] text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2"
                style={{ minHeight: '44px' }}
                whileHover={{ scale: otp.every(digit => digit) && !isLoading ? 1.02 : 1 }}
                whileTap={{ scale: otp.every(digit => digit) && !isLoading ? 0.98 : 1 }}
              >
                {isLoading ? (
                  <Loader2 size={18} className="animate-spin" />
                ) : (
                  <>
                    Verify & Continue
                    <CheckCircle size={18} />
                  </>
                )}
              </motion.button>

              <div className="text-center">
                {resendTimer > 0 ? (
                  <p className="text-[#AAAAAA] text-sm">
                    Resend OTP in {resendTimer}s
                  </p>
                ) : (
                  <button
                    onClick={handleResendOTP}
                    disabled={isLoading}
                    className="text-blue-400 hover:text-blue-300 text-sm font-medium flex items-center gap-1 mx-auto transition-colors"
                  >
                    <RotateCcw size={14} />
                    Resend OTP
                  </button>
                )}
              </div>
            </div>

            {error && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 text-sm flex items-center gap-2"
              >
                <AlertCircle size={16} />
                {error}
              </motion.div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default OTPLogin;
