<?php

echo "=== COMPLETE OUTFIT TEST ===\n\n";

// Test 1: Create outfit with items (like frontend does)
$outfitData = [
    'name' => 'Complete Test Outfit',
    'description' => 'Testing complete outfit functionality',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'Black',
            'selected_size' => 'M',
            'selected_color_hex' => '#000000',
            'category_type' => 'top',
            'sort_order' => 0,
            'is_primary' => true,
            'price' => 59.99,
            'salePrice' => null
        ],
        [
            'product_id' => 2,
            'selected_color' => 'Blue',
            'selected_size' => 'L',
            'selected_color_hex' => '#0000FF',
            'category_type' => 'top',
            'sort_order' => 1,
            'is_primary' => false,
            'price' => 54.99,
            'salePrice' => null
        ]
    ]
];

echo "1. Creating outfit with items:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/v1/outfits');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($outfitData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "   Status: $httpCode\n";
echo "   Response: $response\n\n";

// Test 2: Get all outfits (should include items)
echo "2. Getting all outfits:\n";
$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, 'http://localhost:8000/api/v1/outfits');
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json'
]);
$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
curl_close($ch2);

echo "   Status: $httpCode2\n";
$outfitsData = json_decode($response2, true);
if ($outfitsData && isset($outfitsData['data']['outfits'])) {
    echo "   Found " . count($outfitsData['data']['outfits']) . " outfits\n";
    foreach ($outfitsData['data']['outfits'] as $outfit) {
        $itemCount = isset($outfit['items']) ? count($outfit['items']) : 0;
        echo "   - {$outfit['name']}: {$itemCount} items\n";
        if ($itemCount > 0) {
            foreach ($outfit['items'] as $item) {
                echo "     * Product {$item['product_id']}: {$item['name']} ({$item['selected_color']}, {$item['selected_size']})\n";
            }
        }
    }
} else {
    echo "   Response: $response2\n";
}

echo "\n=== TEST COMPLETE ===\n";
