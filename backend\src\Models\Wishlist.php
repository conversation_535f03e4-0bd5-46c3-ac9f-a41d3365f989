<?php

namespace Wolffoxx\Models;

use Wolffoxx\Config\Database;

/**
 * Wishlist Model
 * 
 * Handles wishlist operations, collections,
 * and wishlist-related database interactions.
 */
class Wishlist extends BaseModel
{
    protected string $table = 'wishlists';
    
    protected array $fillable = [
        'user_id',
        'product_id',
        'notes',
        'priority',
        'is_public'
    ];

    protected array $casts = [
        'user_id' => 'integer',
        'product_id' => 'integer',
        'is_public' => 'boolean'
    ];

    /**
     * Get user's wishlist items with product details
     */
    public function getUserWishlist(int $userId, array $filters = []): array
    {
        try {
            $sql = "SELECT w.*, 
                           p.name, p.price, p.sale_price, p.category, p.is_active,
                           p.average_rating, p.total_reviews,
                           pi.image_url as primary_image,
                           pc.name as primary_color, pc.hex_value as primary_color_hex
                    FROM {$this->table} w
                    INNER JOIN products p ON w.product_id = p.id
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    LEFT JOIN product_colors pc ON p.id = pc.product_id AND pc.sort_order = 0
                    WHERE w.user_id = ? AND p.is_active = 1";

            $params = [$userId];

            // Apply filters
            if (!empty($filters['category'])) {
                $sql .= " AND p.category = ?";
                $params[] = $filters['category'];
            }

            if (!empty($filters['priority'])) {
                $sql .= " AND w.priority = ?";
                $params[] = $filters['priority'];
            }

            if (!empty($filters['price_min'])) {
                $sql .= " AND COALESCE(p.sale_price, p.price) >= ?";
                $params[] = $filters['price_min'];
            }

            if (!empty($filters['price_max'])) {
                $sql .= " AND COALESCE(p.sale_price, p.price) <= ?";
                $params[] = $filters['price_max'];
            }

            // Add sorting
            $sortBy = $filters['sort_by'] ?? 'created_at';
            $sortOrder = $filters['sort_order'] ?? 'DESC';
            
            $allowedSortFields = ['created_at', 'name', 'price', 'priority'];
            if (in_array($sortBy, $allowedSortFields)) {
                if ($sortBy === 'price') {
                    $sql .= " ORDER BY COALESCE(p.sale_price, p.price) {$sortOrder}";
                } elseif ($sortBy === 'name') {
                    $sql .= " ORDER BY p.name {$sortOrder}";
                } else {
                    $sql .= " ORDER BY w.{$sortBy} {$sortOrder}";
                }
            }

            // Add pagination
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . (int)$filters['limit'];
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . (int)$filters['offset'];
                }
            }

            $statement = Database::execute($sql, $params);
            $results = $statement->fetchAll();

            return array_map([$this, 'processWishlistItem'], $results);

        } catch (\Exception $e) {
            error_log('Get user wishlist failed: ' . $e->getMessage() . ' for user_id: ' . $userId);
            throw $e;
        }
    }

    /**
     * Add item to wishlist
     */
    public function addItem(int $userId, int $productId, array $options = []): ?array
    {
        try {
            // Check if item already exists
            if ($this->isInWishlist($userId, $productId)) {
                throw new \Exception('Item already in wishlist');
            }

            // Verify product exists and is active
            $productModel = new Product();
            $product = $productModel->findById($productId);
            
            if (!$product || !$product['is_active']) {
                throw new \Exception('Product not found or inactive');
            }

            $data = [
                'user_id' => $userId,
                'product_id' => $productId,
                'notes' => $options['notes'] ?? null,
                'priority' => $options['priority'] ?? 'medium',
                'is_public' => $options['is_public'] ?? false
            ];

            $wishlistItem = $this->create($data);

            // Log analytics
            $this->logWishlistAnalytics($userId, $productId, 'added', $options);

            // Item added to wishlist successfully

            return $wishlistItem;

        } catch (\Exception $e) {
            $this->logger->error('Add to wishlist failed', [
                'user_id' => $userId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Remove item from wishlist
     */
    public function removeItem(int $userId, int $productId): bool
    {
        try {
            $sql = "DELETE FROM {$this->table} WHERE user_id = ? AND product_id = ?";
            $statement = Database::execute($sql, [$userId, $productId]);
            
            $removed = $statement->rowCount() > 0;

            if ($removed) {
                // Log analytics
                $this->logWishlistAnalytics($userId, $productId, 'removed');

                $this->logger->info('Item removed from wishlist', [
                    'user_id' => $userId,
                    'product_id' => $productId
                ]);
            }

            return $removed;

        } catch (\Exception $e) {
            $this->logger->error('Remove from wishlist failed', [
                'user_id' => $userId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Check if item is in user's wishlist
     */
    public function isInWishlist(int $userId, int $productId): bool
    {
        try {
            $sql = "SELECT 1 FROM {$this->table} WHERE user_id = ? AND product_id = ? LIMIT 1";
            $statement = Database::execute($sql, [$userId, $productId]);
            
            return $statement->fetch() !== false;

        } catch (\Exception $e) {
            $this->logger->error('Wishlist check failed', [
                'user_id' => $userId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get wishlist item count for user
     */
    public function getUserWishlistCount(int $userId): int
    {
        try {
            $sql = "SELECT COUNT(*) as count 
                    FROM {$this->table} w
                    INNER JOIN products p ON w.product_id = p.id
                    WHERE w.user_id = ? AND p.is_active = 1";
            
            $statement = Database::execute($sql, [$userId]);
            $result = $statement->fetch();

            return (int)$result['count'];

        } catch (\Exception $e) {
            $this->logger->error('Get wishlist count failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return 0;
        }
    }

    /**
     * Update wishlist item
     */
    public function updateItem(int $userId, int $productId, array $data): ?array
    {
        try {
            $allowedFields = ['notes', 'priority', 'is_public'];
            $updateData = array_intersect_key($data, array_flip($allowedFields));

            if (empty($updateData)) {
                throw new \Exception('No valid fields to update');
            }

            $setClause = [];
            $params = [];
            
            foreach ($updateData as $field => $value) {
                $setClause[] = "{$field} = ?";
                $params[] = $value;
            }

            $params[] = $userId;
            $params[] = $productId;

            $sql = "UPDATE {$this->table} SET " . implode(', ', $setClause) . ", updated_at = NOW() 
                    WHERE user_id = ? AND product_id = ?";
            
            $statement = Database::execute($sql, $params);

            if ($statement->rowCount() > 0) {
                $this->logger->info('Wishlist item updated', [
                    'user_id' => $userId,
                    'product_id' => $productId,
                    'updated_fields' => array_keys($updateData)
                ]);

                return $this->getWishlistItem($userId, $productId);
            }

            return null;

        } catch (\Exception $e) {
            $this->logger->error('Update wishlist item failed', [
                'user_id' => $userId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get single wishlist item
     */
    public function getWishlistItem(int $userId, int $productId): ?array
    {
        try {
            $sql = "SELECT w.*, 
                           p.name, p.price, p.sale_price, p.category,
                           pi.image_url as primary_image
                    FROM {$this->table} w
                    INNER JOIN products p ON w.product_id = p.id
                    LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_primary = 1
                    WHERE w.user_id = ? AND w.product_id = ?";
            
            $statement = Database::execute($sql, [$userId, $productId]);
            $result = $statement->fetch();

            return $result ? $this->processWishlistItem($result) : null;

        } catch (\Exception $e) {
            $this->logger->error('Get wishlist item failed', [
                'user_id' => $userId,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get wishlist statistics for user
     */
    public function getUserWishlistStats(int $userId): array
    {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_items,
                        COUNT(CASE WHEN w.priority = 'high' THEN 1 END) as high_priority,
                        COUNT(CASE WHEN w.priority = 'medium' THEN 1 END) as medium_priority,
                        COUNT(CASE WHEN w.priority = 'low' THEN 1 END) as low_priority,
                        AVG(COALESCE(p.sale_price, p.price)) as average_price,
                        SUM(COALESCE(p.sale_price, p.price)) as total_value,
                        COUNT(DISTINCT p.category) as categories_count
                    FROM {$this->table} w
                    INNER JOIN products p ON w.product_id = p.id
                    WHERE w.user_id = ? AND p.is_active = 1";
            
            $statement = Database::execute($sql, [$userId]);
            $result = $statement->fetch();

            return [
                'total_items' => (int)$result['total_items'],
                'priority_breakdown' => [
                    'high' => (int)$result['high_priority'],
                    'medium' => (int)$result['medium_priority'],
                    'low' => (int)$result['low_priority']
                ],
                'average_price' => round((float)$result['average_price'], 2),
                'total_value' => round((float)$result['total_value'], 2),
                'categories_count' => (int)$result['categories_count']
            ];

        } catch (\Exception $e) {
            $this->logger->error('Get wishlist stats failed', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Process wishlist item result
     */
    private function processWishlistItem(array $item): array
    {
        $item = $this->processResult($item);
        
        // Calculate savings if on sale
        if ($item['sale_price'] && $item['sale_price'] < $item['price']) {
            $item['savings'] = round($item['price'] - $item['sale_price'], 2);
            $item['savings_percentage'] = round((($item['price'] - $item['sale_price']) / $item['price']) * 100, 1);
        }

        return $item;
    }

    /**
     * Log wishlist analytics
     */
    private function logWishlistAnalytics(int $userId, int $productId, string $action, array $context = []): void
    {
        try {
            $sql = "INSERT INTO wishlist_analytics (user_id, product_id, action, source, device_type, session_id) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            
            $params = [
                $userId,
                $productId,
                $action,
                $context['source'] ?? 'api',
                $context['device_type'] ?? 'unknown',
                $context['session_id'] ?? session_id()
            ];

            Database::execute($sql, $params);

        } catch (\Exception $e) {
            $this->logger->warning('Failed to log wishlist analytics', [
                'user_id' => $userId,
                'product_id' => $productId,
                'action' => $action,
                'error' => $e->getMessage()
            ]);
        }
    }
}
