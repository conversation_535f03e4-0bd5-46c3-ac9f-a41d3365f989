<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Cart;
use Wolffoxx\Models\Product;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Utils\Response;
use Wolffoxx\Utils\Validator;
use Wolffoxx\Utils\Logger;

/**
 * Cart Controller
 * 
 * Handles shopping cart operations including add, remove, update items
 */
class CartController extends BaseController
{
    private Cart $cartModel;
    private Product $productModel;
    private Logger $logger;

    public function __construct()
    {
        $this->cartModel = new Cart();
        $this->productModel = new Product();
        $this->logger = new Logger('cart');
    }

    /**
     * Get user's cart
     */
    public function getCart(array $params = []): void
    {
        try {
            $userId = 3; // Default test user

            // Get cart items for user
            $sql = "SELECT ci.*, c.user_id
                    FROM cart_items ci
                    JOIN carts c ON ci.cart_id = c.id
                    WHERE c.user_id = ?
                    ORDER BY ci.created_at DESC";

            $stmt = \Wolffoxx\Config\Database::execute($sql, [$userId]);
            $items = $stmt->fetchAll();

            // Calculate summary
            $subtotal = 0;
            $totalItems = 0;

            foreach ($items as $item) {
                $price = $item['sale_price'] ?? $item['unit_price'];
                $subtotal += $price * $item['quantity'];
                $totalItems += $item['quantity'];
            }

            Response::success([
                'items' => $items,
                'summary' => [
                    'subtotal' => $subtotal,
                    'total_items' => $totalItems,
                    'item_count' => count($items)
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Get cart failed: ' . $e->getMessage());
            Response::error('Failed to retrieve cart');
        }
    }

    /**
     * Add item to cart
     */
    public function addItem(array $params = []): void
    {
        try {
            $input = json_decode(file_get_contents('php://input'), true);
            $userId = 3; // Default test user

            // Get or create cart for user
            $cartSql = "SELECT id FROM carts WHERE user_id = ? LIMIT 1";
            $cartStmt = \Wolffoxx\Config\Database::execute($cartSql, [$userId]);
            $cart = $cartStmt->fetch();

            if (!$cart) {
                // Create new cart
                $createCartSql = "INSERT INTO carts (user_id, created_at, updated_at) VALUES (?, NOW(), NOW())";
                \Wolffoxx\Config\Database::execute($createCartSql, [$userId]);
                $cartId = \Wolffoxx\Config\Database::getConnection()->lastInsertId();
            } else {
                $cartId = $cart['id'];
            }

            // Insert cart item
            $itemSql = "INSERT INTO cart_items (
                cart_id, product_id, quantity, selected_color, selected_size,
                selected_color_hex, unit_price, sale_price, product_name,
                product_image, product_sku, outfit_id, outfit_name, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())";

            $itemParams = [
                $cartId,
                $input['product_id'] ?? 1,
                $input['quantity'] ?? 1,
                $input['selected_color'] ?? 'Default',
                $input['selected_size'] ?? 'M',
                $input['selected_color_hex'] ?? '#000000',
                $input['unit_price'] ?? 59.99,
                $input['sale_price'] ?? null,
                $input['product_name'] ?? 'Test Product',
                $input['product_image'] ?? null,
                $input['product_sku'] ?? 'TEST001',
                $input['outfit_id'] ?? null,
                $input['outfit_name'] ?? null
            ];

            \Wolffoxx\Config\Database::execute($itemSql, $itemParams);
            $itemId = \Wolffoxx\Config\Database::getConnection()->lastInsertId();

            Response::success([
                'message' => 'Item added to cart successfully',
                'item' => [
                    'id' => $itemId,
                    'cart_id' => $cartId,
                    'product_id' => $input['product_id'] ?? 1,
                    'product_name' => $input['product_name'] ?? 'Test Product',
                    'quantity' => $input['quantity'] ?? 1,
                    'selected_color' => $input['selected_color'] ?? 'Default',
                    'selected_size' => $input['selected_size'] ?? 'M',
                    'unit_price' => $input['unit_price'] ?? 59.99
                ]
            ]);

        } catch (\Exception $e) {
            error_log('Add to cart failed: ' . $e->getMessage());
            Response::error('Failed to add item to cart: ' . $e->getMessage());
        }
    }

    /**
     * Update cart item quantity
     */
    public function updateItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $itemId = (int)($params['id'] ?? 0);
            if (!$itemId) {
                Response::error('Item ID is required', 400);
                return;
            }

            $input = $this->getJsonInput();

            // Validate input
            $validator = new Validator($input, [
                'quantity' => 'required|integer|min:0'
            ]);

            if (!$validator->validate()) {
                Response::validationError($validator->getErrors());
                return;
            }

            $data = $validator->getValidatedData();

            // Update item quantity
            $success = $this->cartModel->updateItemQuantity($itemId, $data['quantity']);

            if ($success) {
                // Get updated cart
                $cart = $this->cartModel->getOrCreateUserCart($userId);
                $updatedCart = $this->cartModel->getCartWithItems($cart['id']);
                
                $this->logger->info('Cart item updated', [
                    'user_id' => $userId,
                    'item_id' => $itemId,
                    'new_quantity' => $data['quantity']
                ]);

                Response::success([
                    'message' => 'Cart item updated successfully',
                    'cart' => $updatedCart
                ]);
            } else {
                Response::error('Failed to update cart item');
            }

        } catch (\Exception $e) {
            $this->logger->error('Update cart item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'item_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to update cart item');
        }
    }

    /**
     * Remove item from cart
     */
    public function removeItem(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $itemId = (int)($params['id'] ?? 0);
            if (!$itemId) {
                Response::error('Item ID is required', 400);
                return;
            }

            // Remove item
            $success = $this->cartModel->removeItem($itemId);

            if ($success) {
                // Get updated cart
                $cart = $this->cartModel->getOrCreateUserCart($userId);
                $updatedCart = $this->cartModel->getCartWithItems($cart['id']);
                
                $this->logger->info('Item removed from cart', [
                    'user_id' => $userId,
                    'item_id' => $itemId
                ]);

                Response::success([
                    'message' => 'Item removed from cart successfully',
                    'cart' => $updatedCart
                ]);
            } else {
                Response::error('Failed to remove item from cart');
            }

        } catch (\Exception $e) {
            $this->logger->error('Remove cart item failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'item_id' => $params['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to remove cart item');
        }
    }

    /**
     * Clear entire cart
     */
    public function clearCart(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $cart = $this->cartModel->getOrCreateUserCart($userId);
            $success = $this->cartModel->clearCart($cart['id']);

            if ($success) {
                $this->logger->info('Cart cleared', [
                    'user_id' => $userId,
                    'cart_id' => $cart['id']
                ]);

                Response::success([
                    'message' => 'Cart cleared successfully'
                ]);
            } else {
                Response::error('Failed to clear cart');
            }

        } catch (\Exception $e) {
            $this->logger->error('Clear cart failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to clear cart');
        }
    }

    /**
     * Get cart count
     */
    public function getCartCount(array $params = []): void
    {
        try {
            $userId = AuthMiddleware::getCurrentUserId();
            
            if (!$userId) {
                Response::unauthorized();
                return;
            }

            $count = $this->cartModel->getUserCartCount($userId);

            Response::success([
                'count' => $count
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Get cart count failed', [
                'user_id' => AuthMiddleware::getCurrentUserId(),
                'error' => $e->getMessage()
            ]);
            Response::error('Failed to get cart count');
        }
    }
}
