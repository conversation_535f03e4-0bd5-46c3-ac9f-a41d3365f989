import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';
import { X, ShoppingBag, Heart, User } from 'lucide-react';
import OTPLogin from '../components/auth/OTPLogin';
import { useAuth } from '../context/AuthContext';
import drawingLogo from '../assets/logo45.svg';

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isAuthenticated } = useAuth();

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = location.state?.from?.pathname || '/';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Handle successful login
  const handleLoginSuccess = (data) => {
    const from = location.state?.from?.pathname || '/';
    
    // Show success message briefly
    setTimeout(() => {
      navigate(from, { replace: true });
    }, 500);
  };

  // Handle close/back
  const handleClose = () => {
    navigate(-1);
  };

  return (
    <div className="min-h-screen bg-black flex flex-col">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between p-4 md:p-6 border-b border-[#2a2a2a]"
      >
        <div className="flex items-center gap-3">
          <img
            src={drawingLogo}
            alt="WOLFFOXX"
            className="h-8 w-auto filter brightness-90"
          />
          <div className="hidden sm:block">
            <h1 className="text-white font-semibold">Welcome Back</h1>
            <p className="text-[#AAAAAA] text-sm">Sign in to your account</p>
          </div>
        </div>
        
        <button
          onClick={handleClose}
          className="p-2 text-[#6a6a6a] hover:text-white transition-colors rounded-lg hover:bg-[#2a2a2a]"
        >
          <X size={24} />
        </button>
      </motion.header>

      {/* Main Content */}
      <div className="flex-1 flex items-center justify-center p-4 md:p-6">
        <div className="w-full max-w-md">
          {/* Login Form */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-2xl p-6 md:p-8 shadow-2xl"
          >
            <OTPLogin
              onSuccess={handleLoginSuccess}
              onClose={handleClose}
            />
          </motion.div>

          {/* Benefits Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mt-8 space-y-4"
          >
            <h3 className="text-white font-semibold text-center mb-4">
              Why create an account?
            </h3>
            
            <div className="grid gap-3">
              <div className="flex items-center gap-3 p-3 bg-[#1a1a1a] rounded-lg border border-[#2a2a2a]">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <ShoppingBag size={16} className="text-blue-400" />
                </div>
                <div>
                  <p className="text-white text-sm font-medium">Faster Checkout</p>
                  <p className="text-[#AAAAAA] text-xs">Save your details for quick ordering</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-[#1a1a1a] rounded-lg border border-[#2a2a2a]">
                <div className="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                  <Heart size={16} className="text-red-400" />
                </div>
                <div>
                  <p className="text-white text-sm font-medium">Wishlist & Outfits</p>
                  <p className="text-[#AAAAAA] text-xs">Save your favorite items and outfits</p>
                </div>
              </div>

              <div className="flex items-center gap-3 p-3 bg-[#1a1a1a] rounded-lg border border-[#2a2a2a]">
                <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                  <User size={16} className="text-green-400" />
                </div>
                <div>
                  <p className="text-white text-sm font-medium">Order Tracking</p>
                  <p className="text-[#AAAAAA] text-xs">Track your orders and delivery status</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Terms */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
            className="mt-6 text-center"
          >
            <p className="text-[#6a6a6a] text-xs leading-relaxed">
              By continuing, you agree to our{' '}
              <button className="text-blue-400 hover:text-blue-300 underline">
                Terms of Service
              </button>{' '}
              and{' '}
              <button className="text-blue-400 hover:text-blue-300 underline">
                Privacy Policy
              </button>
            </p>
          </motion.div>
        </div>
      </div>

      {/* Background Pattern */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl" />
      </div>
    </div>
  );
};

export default LoginPage;
