<?php

namespace Wolffoxx\Services;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;
use <PERSON>oxx\Utils\Logger;

/**
 * Email Service
 * 
 * Handles email sending with template support,
 * SMTP configuration, and error handling.
 */
class EmailService
{
    private Logger $logger;
    private array $config;

    public function __construct()
    {
        $this->logger = new Logger('email');
        $this->config = [
            'host' => $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com',
            'port' => (int)($_ENV['MAIL_PORT'] ?? 587),
            'username' => $_ENV['MAIL_USERNAME'] ?? '',
            'password' => $_ENV['MAIL_PASSWORD'] ?? '',
            'encryption' => $_ENV['MAIL_ENCRYPTION'] ?? 'tls',
            'from_address' => $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
            'from_name' => $_ENV['MAIL_FROM_NAME'] ?? 'Wolffoxx'
        ];
    }

    /**
     * Send email with template
     */
    public function send(string $to, string $subject, string $template, array $data = []): bool
    {
        try {
            $mail = $this->createMailer();

            // Set recipient
            $mail->addAddress($to);

            // Set subject
            $mail->Subject = $subject;

            // Load and render template
            $htmlBody = $this->renderTemplate($template, $data);
            $textBody = $this->htmlToText($htmlBody);

            // Set body
            $mail->isHTML(true);
            $mail->Body = $htmlBody;
            $mail->AltBody = $textBody;

            // Send email
            $result = $mail->send();

            if ($result) {
                $this->logger->info('Email sent successfully', [
                    'to' => $to,
                    'subject' => $subject,
                    'template' => $template
                ]);
            }

            return $result;

        } catch (Exception $e) {
            $this->logger->error('Email sending failed', [
                'to' => $to,
                'subject' => $subject,
                'template' => $template,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send plain text email
     */
    public function sendText(string $to, string $subject, string $message): bool
    {
        try {
            $mail = $this->createMailer();

            // Set recipient
            $mail->addAddress($to);

            // Set subject and body
            $mail->Subject = $subject;
            $mail->Body = $message;

            // Send email
            $result = $mail->send();

            if ($result) {
                $this->logger->info('Text email sent successfully', [
                    'to' => $to,
                    'subject' => $subject
                ]);
            }

            return $result;

        } catch (Exception $e) {
            $this->logger->error('Text email sending failed', [
                'to' => $to,
                'subject' => $subject,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send email to multiple recipients
     */
    public function sendBulk(array $recipients, string $subject, string $template, array $data = []): array
    {
        $results = [];

        foreach ($recipients as $recipient) {
            $email = is_array($recipient) ? $recipient['email'] : $recipient;
            $personalData = is_array($recipient) ? array_merge($data, $recipient) : $data;
            
            $results[$email] = $this->send($email, $subject, $template, $personalData);
        }

        $this->logger->info('Bulk email sent', [
            'total_recipients' => count($recipients),
            'successful' => count(array_filter($results)),
            'failed' => count(array_filter($results, fn($r) => !$r))
        ]);

        return $results;
    }

    /**
     * Create PHPMailer instance
     */
    private function createMailer(): PHPMailer
    {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->isSMTP();
        $mail->Host = $this->config['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $this->config['username'];
        $mail->Password = $this->config['password'];
        $mail->SMTPSecure = $this->config['encryption'];
        $mail->Port = $this->config['port'];

        // Set sender
        $mail->setFrom($this->config['from_address'], $this->config['from_name']);

        // Set encoding
        $mail->CharSet = 'UTF-8';

        // Enable debug in development
        if ($_ENV['APP_ENV'] === 'development') {
            $mail->SMTPDebug = SMTP::DEBUG_SERVER;
            $mail->Debugoutput = function($str, $level) {
                $this->logger->debug('SMTP Debug', ['message' => $str, 'level' => $level]);
            };
        }

        return $mail;
    }

    /**
     * Render email template
     */
    private function renderTemplate(string $template, array $data = []): string
    {
        $templatePath = __DIR__ . '/../../templates/' . $template . '.php';

        if (!file_exists($templatePath)) {
            // Return basic template if file doesn't exist
            return $this->getBasicTemplate($data);
        }

        // Extract data variables
        extract($data);

        // Start output buffering
        ob_start();

        // Include template
        include $templatePath;

        // Get content and clean buffer
        $content = ob_get_clean();

        return $content;
    }

    /**
     * Get basic email template
     */
    private function getBasicTemplate(array $data = []): string
    {
        $siteName = $this->config['from_name'];
        $siteUrl = $_ENV['FRONTEND_URL'] ?? 'https://wolffoxx.com';

        return "
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>{$siteName}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #0f172a; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
                .button { display: inline-block; padding: 12px 24px; background: #3b82f6; color: white; text-decoration: none; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>{$siteName}</h1>
                </div>
                <div class='content'>
                    " . ($data['content'] ?? 'Thank you for using our service.') . "
                </div>
                <div class='footer'>
                    <p>&copy; " . date('Y') . " {$siteName}. All rights reserved.</p>
                    <p><a href='{$siteUrl}'>Visit our website</a></p>
                </div>
            </div>
        </body>
        </html>";
    }

    /**
     * Convert HTML to plain text
     */
    private function htmlToText(string $html): string
    {
        // Remove HTML tags
        $text = strip_tags($html);
        
        // Convert HTML entities
        $text = html_entity_decode($text, ENT_QUOTES, 'UTF-8');
        
        // Clean up whitespace
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Validate email address
     */
    public function validateEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * Test email configuration
     */
    public function testConnection(): bool
    {
        try {
            $mail = $this->createMailer();
            
            // Test SMTP connection
            $mail->smtpConnect();
            $mail->smtpClose();

            $this->logger->info('Email connection test successful');
            return true;

        } catch (Exception $e) {
            $this->logger->error('Email connection test failed', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get email configuration (without sensitive data)
     */
    public function getConfig(): array
    {
        return [
            'host' => $this->config['host'],
            'port' => $this->config['port'],
            'encryption' => $this->config['encryption'],
            'from_address' => $this->config['from_address'],
            'from_name' => $this->config['from_name']
        ];
    }
}
