import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Edit3, 
  Phone, 
  Mail, 
  Calendar, 
  MapPin, 
  Heart, 
  ShoppingBag, 
  Package, 
  Settings, 
  LogOut,
  Camera,
  Save,
  X,
  AlertCircle,
  CheckCircle,
  Shirt
} from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import { useWishlist } from '../context/WishlistContext';
import { useCart } from '../context/CartContext';

const ProfilePage = () => {
  const { user, logout, updateProfile, isLoading, error, refreshProfile } = useAuth();
  const { totalWishlistItems } = useWishlist();
  const { totalItems } = useCart();
  const navigate = useNavigate();

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    date_of_birth: '',
    gender: ''
  });
  const [updateSuccess, setUpdateSuccess] = useState(false);

  // Initialize form with user data
  useEffect(() => {
    if (user) {
      setEditForm({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        email: user.email || '',
        date_of_birth: user.date_of_birth || '',
        gender: user.gender || ''
      });
    }
  }, [user]);

  // Refresh profile on mount
  useEffect(() => {
    refreshProfile();
  }, []);

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle save profile
  const handleSaveProfile = async () => {
    const result = await updateProfile(editForm);
    
    if (result.success) {
      setIsEditing(false);
      setUpdateSuccess(true);
      setTimeout(() => setUpdateSuccess(false), 3000);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  // Format phone number for display
  const formatPhoneDisplay = (phone) => {
    if (!phone) return '';
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 12 && cleaned.startsWith('91')) {
      const number = cleaned.slice(2);
      return `+91 ${number.slice(0, 5)} ${number.slice(5)}`;
    }
    return phone;
  };

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-600/20 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
          <p className="text-[#AAAAAA]">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black pt-16">
      <div className="container mx-auto px-4 py-6 max-w-4xl">
        {/* Success Message */}
        <AnimatePresence>
          {updateSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="mb-6 p-4 bg-green-500/10 border border-green-500/20 rounded-lg text-green-400 flex items-center gap-2"
            >
              <CheckCircle size={20} />
              Profile updated successfully!
            </motion.div>
          )}
        </AnimatePresence>

        {/* Profile Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-2xl p-6 mb-6"
        >
          <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
            {/* Profile Image */}
            <div className="relative">
              <div className="w-20 h-20 md:w-24 md:h-24 bg-gradient-to-br from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                {user.profile_image ? (
                  <img
                    src={user.profile_image}
                    alt="Profile"
                    className="w-full h-full rounded-full object-cover"
                  />
                ) : (
                  <User size={32} className="text-white" />
                )}
              </div>
              <button className="absolute -bottom-1 -right-1 w-8 h-8 bg-blue-600 hover:bg-blue-700 rounded-full flex items-center justify-center transition-colors">
                <Camera size={14} className="text-white" />
              </button>
            </div>

            {/* Profile Info */}
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <h1 className="text-2xl font-bold text-white">
                    {user.first_name} {user.last_name}
                  </h1>
                  <p className="text-[#AAAAAA] flex items-center gap-2 mt-1">
                    <Phone size={16} />
                    {formatPhoneDisplay(user.phone)}
                  </p>
                  {user.email && (
                    <p className="text-[#AAAAAA] flex items-center gap-2 mt-1">
                      <Mail size={16} />
                      {user.email}
                    </p>
                  )}
                </div>

                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                  style={{ minHeight: '44px' }}
                >
                  <Edit3 size={16} />
                  {isEditing ? 'Cancel' : 'Edit Profile'}
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Edit Profile Form */}
        <AnimatePresence>
          {isEditing && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-2xl p-6 mb-6 overflow-hidden"
            >
              <h2 className="text-xl font-semibold text-white mb-4">Edit Profile</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={editForm.first_name}
                    onChange={(e) => handleInputChange('first_name', e.target.value)}
                    className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={editForm.last_name}
                    onChange={(e) => handleInputChange('last_name', e.target.value)}
                    className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                    Email (Optional)
                  </label>
                  <input
                    type="email"
                    value={editForm.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    value={editForm.date_of_birth}
                    onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                    className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }}
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-[#AAAAAA] mb-2">
                    Gender
                  </label>
                  <select
                    value={editForm.gender}
                    onChange={(e) => handleInputChange('gender', e.target.value)}
                    className="w-full px-4 py-3 bg-[#1a1a1a] border border-[#404040] rounded-lg text-white focus:border-blue-500 focus:ring-1 focus:ring-blue-500 transition-colors"
                    style={{ minHeight: '44px' }}
                  >
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    <option value="other">Other</option>
                    <option value="prefer_not_to_say">Prefer not to say</option>
                  </select>
                </div>
              </div>

              {error && (
                <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-400 flex items-center gap-2">
                  <AlertCircle size={16} />
                  {error}
                </div>
              )}

              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleSaveProfile}
                  disabled={isLoading}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-[#404040] text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
                  style={{ minHeight: '44px' }}
                >
                  {isLoading ? (
                    <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                  ) : (
                    <Save size={16} />
                  )}
                  Save Changes
                </button>
                
                <button
                  onClick={() => setIsEditing(false)}
                  className="bg-[#2a2a2a] hover:bg-[#404040] text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
                  style={{ minHeight: '44px' }}
                >
                  <X size={16} />
                  Cancel
                </button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
        >
          <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-4 text-center">
            <Heart size={24} className="text-red-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">{totalWishlistItems}</p>
            <p className="text-[#AAAAAA] text-sm">Wishlist Items</p>
          </div>

          <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-4 text-center">
            <ShoppingBag size={24} className="text-blue-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">{totalItems}</p>
            <p className="text-[#AAAAAA] text-sm">Cart Items</p>
          </div>

          <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-4 text-center">
            <Package size={24} className="text-green-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">0</p>
            <p className="text-[#AAAAAA] text-sm">Orders</p>
          </div>

          <div className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-xl p-4 text-center">
            <Shirt size={24} className="text-purple-400 mx-auto mb-2" />
            <p className="text-2xl font-bold text-white">0</p>
            <p className="text-[#AAAAAA] text-sm">Outfits</p>
          </div>
        </motion.div>

        {/* Profile Details */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-[#0a0a0a] border border-[#2a2a2a] rounded-2xl p-6 mb-6"
        >
          <h2 className="text-xl font-semibold text-white mb-4">Profile Details</h2>
          
          <div className="space-y-4">
            {user.date_of_birth && (
              <div className="flex items-center gap-3">
                <Calendar size={20} className="text-[#6a6a6a]" />
                <div>
                  <p className="text-[#AAAAAA] text-sm">Date of Birth</p>
                  <p className="text-white">{formatDate(user.date_of_birth)}</p>
                </div>
              </div>
            )}

            {user.gender && (
              <div className="flex items-center gap-3">
                <User size={20} className="text-[#6a6a6a]" />
                <div>
                  <p className="text-[#AAAAAA] text-sm">Gender</p>
                  <p className="text-white capitalize">{user.gender.replace('_', ' ')}</p>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3">
              <Calendar size={20} className="text-[#6a6a6a]" />
              <div>
                <p className="text-[#AAAAAA] text-sm">Member Since</p>
                <p className="text-white">{formatDate(user.created_at)}</p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Logout Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="text-center"
        >
          <button
            onClick={handleLogout}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 mx-auto transition-colors"
            style={{ minHeight: '44px' }}
          >
            <LogOut size={16} />
            Logout
          </button>
        </motion.div>
      </div>
    </div>
  );
};

export default ProfilePage;
