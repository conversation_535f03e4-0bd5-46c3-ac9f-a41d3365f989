<?php

echo "=== TESTING OUTFIT CREATION WITH ITEMS ===\n\n";

// Test outfit creation with items
$outfitData = [
    'name' => 'Test Outfit with Items',
    'description' => 'Testing outfit creation with items',
    'occasion' => 'casual',
    'season' => 'all',
    'is_public' => false,
    'items' => [
        [
            'product_id' => 1,
            'selected_color' => 'Black',
            'selected_size' => 'M',
            'selected_color_hex' => '#000000',
            'category_type' => 'top',
            'is_primary' => true
        ],
        [
            'product_id' => 2,
            'selected_color' => 'Blue',
            'selected_size' => 'L',
            'selected_color_hex' => '#0000FF',
            'category_type' => 'top',
            'is_primary' => false
        ]
    ]
];

echo "1. Testing outfit creation API:\n";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost:8000/api/v1/outfits');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($outfitData));
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Authorization: Bearer test-token' // You might need to adjust this
]);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "Status: $httpCode\n";
echo "Response: $response\n\n";

// Check database directly
echo "2. Checking database for outfit items:\n";
require_once 'config/database.php';
use Wolffoxx\Config\Database;

try {
    Database::init();
    
    echo "   Outfits in database:\n";
    $stmt = Database::execute('SELECT id, name, description FROM outfits ORDER BY id DESC LIMIT 3');
    $outfits = $stmt->fetchAll();
    foreach($outfits as $outfit) {
        echo "   - Outfit {$outfit['id']}: {$outfit['name']}\n";
        
        // Check items for this outfit
        $itemStmt = Database::execute('SELECT * FROM outfit_items WHERE outfit_id = ?', [$outfit['id']]);
        $items = $itemStmt->fetchAll();
        echo "     Items: " . count($items) . "\n";
        foreach($items as $item) {
            echo "       - Product {$item['product_id']} ({$item['selected_color']}, {$item['selected_size']})\n";
        }
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}

echo "\n=== TEST COMPLETE ===\n";
