<?php

namespace Wolffoxx\Config;

use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\SignatureInvalidException;
use Wolffoxx\Utils\Logger;

/**
 * JWT Configuration and Token Management
 * 
 * Handles JWT token generation, validation, and refresh
 * with secure configuration and error handling.
 */
class JWTConfig
{
    private static string $secret;
    private static string $algorithm = 'HS256';
    private static int $expiry;
    private static int $refreshExpiry;
    private static Logger $logger;

    /**
     * Initialize JWT configuration
     */
    public static function init(): void
    {
        self::$secret = $_ENV['JWT_SECRET'] ?? 'default-secret-change-in-production';
        self::$expiry = (int)($_ENV['JWT_EXPIRY'] ?? 3600); // 1 hour
        self::$refreshExpiry = (int)($_ENV['JWT_REFRESH_EXPIRY'] ?? 604800); // 7 days
        self::$logger = new Logger('jwt');

        // Validate secret strength in production
        if ($_ENV['APP_ENV'] === 'production' && strlen(self::$secret) < 32) {
            throw new \InvalidArgumentException('JWT secret must be at least 32 characters in production');
        }
    }

    /**
     * Generate JWT access token
     */
    public static function generateAccessToken(array $payload): string
    {
        $now = time();
        
        $tokenPayload = array_merge($payload, [
            'iat' => $now,                          // Issued at
            'exp' => $now + self::$expiry,          // Expiration
            'nbf' => $now,                          // Not before
            'iss' => $_ENV['APP_URL'] ?? 'wolffoxx-api', // Issuer
            'aud' => $_ENV['FRONTEND_URL'] ?? 'wolffoxx-frontend', // Audience
            'type' => 'access'
        ]);

        try {
            $token = JWT::encode($tokenPayload, self::$secret, self::$algorithm);
            
            self::$logger->info('Access token generated', [
                'user_id' => $payload['user_id'] ?? null,
                'expires_at' => date('Y-m-d H:i:s', $now + self::$expiry)
            ]);

            return $token;
        } catch (\Exception $e) {
            self::$logger->error('Failed to generate access token', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;
        }
    }

    /**
     * Generate JWT refresh token
     */
    public static function generateRefreshToken(array $payload): string
    {
        $now = time();
        
        $tokenPayload = array_merge($payload, [
            'iat' => $now,
            'exp' => $now + self::$refreshExpiry,
            'nbf' => $now,
            'iss' => $_ENV['APP_URL'] ?? 'wolffoxx-api',
            'aud' => $_ENV['FRONTEND_URL'] ?? 'wolffoxx-frontend',
            'type' => 'refresh'
        ]);

        try {
            $token = JWT::encode($tokenPayload, self::$secret, self::$algorithm);
            
            self::$logger->info('Refresh token generated', [
                'user_id' => $payload['user_id'] ?? null,
                'expires_at' => date('Y-m-d H:i:s', $now + self::$refreshExpiry)
            ]);

            return $token;
        } catch (\Exception $e) {
            self::$logger->error('Failed to generate refresh token', [
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);
            throw $e;
        }
    }

    /**
     * Validate and decode JWT token
     */
    public static function validateToken(string $token): ?array
    {
        try {
            $decoded = JWT::decode($token, new Key(self::$secret, self::$algorithm));
            $payload = (array)$decoded;

            self::$logger->debug('Token validated successfully', [
                'user_id' => $payload['user_id'] ?? null,
                'type' => $payload['type'] ?? 'unknown'
            ]);

            return $payload;

        } catch (ExpiredException $e) {
            self::$logger->warning('Token expired', [
                'token' => substr($token, 0, 20) . '...',
                'error' => $e->getMessage()
            ]);
            return null;

        } catch (SignatureInvalidException $e) {
            self::$logger->error('Invalid token signature', [
                'token' => substr($token, 0, 20) . '...',
                'error' => $e->getMessage()
            ]);
            return null;

        } catch (\Exception $e) {
            self::$logger->error('Token validation failed', [
                'token' => substr($token, 0, 20) . '...',
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Extract token from Authorization header
     */
    public static function extractTokenFromHeader(): ?string
    {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? '';

        if (empty($authHeader)) {
            return null;
        }

        // Check for Bearer token format
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Check if token is expired
     */
    public static function isTokenExpired(array $payload): bool
    {
        $exp = $payload['exp'] ?? 0;
        return time() >= $exp;
    }

    /**
     * Check if token is about to expire (within 5 minutes)
     */
    public static function isTokenExpiringSoon(array $payload): bool
    {
        $exp = $payload['exp'] ?? 0;
        return time() >= ($exp - 300); // 5 minutes before expiry
    }

    /**
     * Refresh access token using refresh token
     */
    public static function refreshAccessToken(string $refreshToken): ?array
    {
        $payload = self::validateToken($refreshToken);
        
        if (!$payload || ($payload['type'] ?? '') !== 'refresh') {
            self::$logger->warning('Invalid refresh token provided');
            return null;
        }

        // Generate new access token
        $newAccessToken = self::generateAccessToken([
            'user_id' => $payload['user_id'],
            'email' => $payload['email'],
            'role' => $payload['role'] ?? 'user'
        ]);

        return [
            'access_token' => $newAccessToken,
            'expires_in' => self::$expiry,
            'token_type' => 'Bearer'
        ];
    }

    /**
     * Generate token pair (access + refresh)
     */
    public static function generateTokenPair(array $userPayload): array
    {
        $accessToken = self::generateAccessToken($userPayload);
        $refreshToken = self::generateRefreshToken($userPayload);

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'expires_in' => self::$expiry,
            'refresh_expires_in' => self::$refreshExpiry,
            'token_type' => 'Bearer'
        ];
    }

    /**
     * Get JWT configuration
     */
    public static function getConfig(): array
    {
        return [
            'algorithm' => self::$algorithm,
            'expiry' => self::$expiry,
            'refresh_expiry' => self::$refreshExpiry,
            'issuer' => $_ENV['APP_URL'] ?? 'wolffoxx-api',
            'audience' => $_ENV['FRONTEND_URL'] ?? 'wolffoxx-frontend'
        ];
    }

    /**
     * Blacklist token (for logout functionality)
     * Note: In production, implement token blacklisting with Redis or database
     */
    public static function blacklistToken(string $token): bool
    {
        // TODO: Implement token blacklisting
        // For now, just log the action
        self::$logger->info('Token blacklisted', [
            'token' => substr($token, 0, 20) . '...'
        ]);
        
        return true;
    }
}
