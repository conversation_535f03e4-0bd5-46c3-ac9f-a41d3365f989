<?php

echo "=== SIMPLE OUTFIT DEBUG ===\n\n";

require_once 'config/database.php';
use Wolffoxx\Config\Database;

try {
    Database::init();
    
    echo "1. Testing database connection:\n";
    $stmt = Database::execute('SELECT 1 as test');
    $result = $stmt->fetch();
    echo "   Database connection: " . ($result['test'] == 1 ? 'OK' : 'FAILED') . "\n\n";
    
    echo "2. Testing outfit_items table structure:\n";
    $stmt = Database::execute('DESCRIBE outfit_items');
    $columns = $stmt->fetchAll();
    echo "   Columns in outfit_items table:\n";
    foreach($columns as $col) {
        echo "   - {$col['Field']} ({$col['Type']})\n";
    }
    
    echo "\n3. Testing manual item insert:\n";
    $outfitId = 10; // Use the last created outfit
    $itemSql = "INSERT INTO outfit_items (outfit_id, product_id, selected_color, selected_size, selected_color_hex, category_type, is_primary, created_at) 
               VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    
    $itemParams = [
        $outfitId,
        1, // product_id
        'Black', // selected_color
        'M', // selected_size
        '#000000', // selected_color_hex
        'top', // category_type
        false // is_primary
    ];
    
    echo "   Inserting item with params: " . json_encode($itemParams) . "\n";
    Database::execute($itemSql, $itemParams);
    echo "   Item inserted successfully!\n";
    
    echo "\n4. Checking items for outfit $outfitId:\n";
    $checkStmt = Database::execute('SELECT * FROM outfit_items WHERE outfit_id = ?', [$outfitId]);
    $items = $checkStmt->fetchAll();
    echo "   Found " . count($items) . " items:\n";
    foreach($items as $item) {
        echo "   - Product {$item['product_id']} ({$item['selected_color']}, {$item['selected_size']})\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";
